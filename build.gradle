plugins {
    id 'org.springframework.boot' version '3.4.8'
    id 'io.spring.dependency-management' version '1.1.7'
    id 'java'
}

group 'athena'
version '1.0.0'

repositories {
    google()
    jcenter()
    mavenCentral()
    gradlePluginPortal()
}

java {
    sourceCompatibility = JavaVersion.VERSION_21
    targetCompatibility = JavaVersion.VERSION_21
    
    compileJava {
        options.compilerArgs.addAll(['-parameters'])
    }
}

apply plugin: 'io.spring.dependency-management'

dependencies {
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    runtimeOnly 'org.springframework.boot:spring-boot-devtools'
    implementation 'org.springframework.boot:spring-boot-configuration-processor'
    implementation group: 'org.springframework.batch', name: 'spring-batch-core', version: '5.2.2'
    implementation group: 'org.apache.commons', name: 'commons-collections4', version: '4.5.0'
    implementation group: 'com.google.code.gson', name: 'gson', version: '2.13.1'
    implementation group: 'commons-codec', name: 'commons-codec', version: '1.18.0'
    implementation group: 'com.mysql', name: 'mysql-connector-j', version: '9.3.0'
    implementation group: 'org.mybatis.spring.boot', name: 'mybatis-spring-boot-starter', version: '3.0.4'
    implementation group: 'org.mybatis', name: 'mybatis', version: '3.5.19'
    implementation group: 'org.mybatis.dynamic-sql', name: 'mybatis-dynamic-sql', version: '1.5.2'
    implementation group: 'org.json', name: 'json', version: '20250107'
    implementation group: 'redis.clients', name: 'jedis', version: '6.0.0'
    implementation group: 'org.mapstruct', name: 'mapstruct', version: '1.6.3'
    compileOnly group: 'org.projectlombok', name: 'lombok', version: '1.18.38'
    annotationProcessor 'org.projectlombok:lombok:1.18.38'
    implementation group: 'org.projectlombok', name: 'lombok-mapstruct-binding', version: '0.2.0'
    implementation group: 'org.mapstruct', name: 'mapstruct-processor', version: '1.6.3'
    annotationProcessor 'org.mapstruct:mapstruct-processor:1.6.3'
    implementation group: 'io.jsonwebtoken', name: 'jjwt-api', version: '0.12.6'
    implementation group: 'org.mongodb', name: 'mongodb-driver-sync', version: '5.5.0'
    implementation group: 'org.mongodb', name: 'bson', version: '5.5.0'
    implementation group: 'org.mongodb', name: 'mongodb-driver-core', version: '5.5.0'
    implementation group: 'org.springframework.data', name: 'spring-data-mongodb', version: '4.4.5'
}

sourceSets {
    main.java.srcDirs += "build/generated/source/apt/main"
}
targetCompatibility = JavaVersion.VERSION_21
