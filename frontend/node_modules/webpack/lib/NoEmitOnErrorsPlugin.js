/*
	MIT License http://www.opensource.org/licenses/mit-license.php
	Author <PERSON> @sokra
*/

"use strict";

/** @typedef {import("./Compiler")} Compiler */

const PLUGIN_NAME = "NoEmitOnErrorsPlugin";

class NoEmitOnErrorsPlugin {
	/**
	 * Apply the plugin
	 * @param {Compiler} compiler the compiler instance
	 * @returns {void}
	 */
	apply(compiler) {
		compiler.hooks.shouldEmit.tap(PLUGIN_NAME, (compilation) => {
			if (compilation.getStats().hasErrors()) return false;
		});
		compiler.hooks.compilation.tap(PLUGIN_NAME, (compilation) => {
			compilation.hooks.shouldRecord.tap(PLUGIN_NAME, () => {
				if (compilation.getStats().hasErrors()) return false;
			});
		});
	}
}

module.exports = NoEmitOnErrorsPlugin;
