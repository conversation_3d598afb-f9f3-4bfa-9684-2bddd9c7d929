import React, { useState } from 'react';
import { farmAPI } from '../services/api';

function FarmManager() {
  const [farms, setFarms] = useState([]);
  const [userId, setUserId] = useState('');
  const [formData, setFormData] = useState({
    location: '',
    tag: '',
    areaMeasured: '',
    description: '',
    crops: []
  });
  const [searchFilter, setSearchFilter] = useState({
    pageNumber: 0,
    pageSize: 10
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleGetFarmsByUser = async () => {
    if (!userId) return;
    setLoading(true);
    setError('');
    try {
      const response = await farmAPI.getFarmsByUser(userId);
      setFarms(response.data);
    } catch (err) {
      setError('Failed to fetch farms');
    }
    setLoading(false);
  };

  const handleQueryFarms = async (isPost = 1) => {
    setLoading(true);
    setError('');
    try {
      const response = await farmAPI.queryFarms(isPost, searchFilter);
      setFarms(response.data);
    } catch (err) {
      setError('Failed to query farms');
    }
    setLoading(false);
  };

  const handleSaveFarm = async () => {
    setLoading(true);
    setError('');
    try {
      const response = await farmAPI.saveFarm(formData);
      alert('Farm saved successfully!');
      setFormData({
        location: '',
        tag: '',
        areaMeasured: '',
        description: '',
        crops: []
      });
    } catch (err) {
      setError('Failed to save farm');
    }
    setLoading(false);
  };

  return (
    <div className="section">
      <h2>Farm Management</h2>
      
      <div className="form">
        <h3>Get Farms by User</h3>
        <input
          type="number"
          placeholder="User ID"
          value={userId}
          onChange={(e) => setUserId(e.target.value)}
        />
        <button onClick={handleGetFarmsByUser} disabled={loading}>
          Get Farms
        </button>
      </div>

      <div className="form">
        <h3>Query Farms</h3>
        <input
          type="number"
          placeholder="Page Number"
          value={searchFilter.pageNumber}
          onChange={(e) => setSearchFilter(prev => ({ ...prev, pageNumber: parseInt(e.target.value) }))}
        />
        <input
          type="number"
          placeholder="Page Size"
          value={searchFilter.pageSize}
          onChange={(e) => setSearchFilter(prev => ({ ...prev, pageSize: parseInt(e.target.value) }))}
        />
        <button onClick={() => handleQueryFarms(0)} disabled={loading}>
          Query Farms
        </button>
        <button onClick={() => handleQueryFarms(1)} disabled={loading}>
          Query Posts as Farms
        </button>
      </div>

      <div className="form">
        <h3>Create Farm</h3>
        <input
          placeholder="Location"
          value={formData.location}
          onChange={(e) => setFormData(prev => ({ ...prev, location: e.target.value }))}
        />
        <input
          placeholder="Tag"
          value={formData.tag}
          onChange={(e) => setFormData(prev => ({ ...prev, tag: e.target.value }))}
        />
        <input
          placeholder="Area Measured"
          value={formData.areaMeasured}
          onChange={(e) => setFormData(prev => ({ ...prev, areaMeasured: e.target.value }))}
        />
        <textarea
          placeholder="Description"
          value={formData.description}
          onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
        />
        <button onClick={handleSaveFarm} disabled={loading}>
          Save Farm
        </button>
      </div>

      {farms.length > 0 && (
        <div>
          <h3>Farms ({farms.length})</h3>
          <table className="table">
            <thead>
              <tr>
                <th>Location</th>
                <th>Tag</th>
                <th>Area</th>
                <th>Description</th>
                <th>Crops</th>
              </tr>
            </thead>
            <tbody>
              {farms.map((farm, index) => (
                <tr key={index}>
                  <td>{farm.location}</td>
                  <td>{farm.tag}</td>
                  <td>{farm.areaMeasured}</td>
                  <td>{farm.description}</td>
                  <td>{farm.crops ? farm.crops.length : 0}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {error && <div className="error">{error}</div>}
      {loading && <div className="loading">Loading...</div>}
    </div>
  );
}

export default FarmManager;