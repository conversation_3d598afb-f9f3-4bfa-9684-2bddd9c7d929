package athena.human.controller.cropshare;

import java.util.List;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import athena.human.domain.data.dto.SearchFilterDto;
import athena.human.domain.data.dto.cropshare.CropsDto;
import athena.human.domain.data.dto.cropshare.FarmsDto;
import athena.human.domain.data.dto.cropshare.Land;
import athena.human.service.cropshare.impl.CropsService;
import athena.human.service.cropshare.impl.FarmsService;

@RestController
@RequestMapping("/land")
public class MainController {

    private final FarmsService farmsService;
    private final CropsService cropsService;

    public MainController(FarmsService farmsService, CropsService cropsService) {
        this.farmsService = farmsService;
        this.cropsService = cropsService;
    }

    @PostMapping("")
    public ResponseEntity<FarmsDto> saveFarm(@RequestBody FarmsDto farm) {
        return ResponseEntity.ok(farmsService.saveFarms(farm));
    }

    @GetMapping("")
    public ResponseEntity<List<FarmsDto>> getFarmsByUser(@RequestParam long userId) {
        return ResponseEntity.ok(farmsService.getFarmsByOwner(userId));
    }

    @GetMapping("crops")
    public ResponseEntity<List<CropsDto>> getCropsByFarmsId(@RequestParam long farmsId) {
        return ResponseEntity.ok(cropsService.getCropsByFarmsId(farmsId));
    }

    @PostMapping("farms")
    public ResponseEntity<List<FarmsDto>> queryForFarms(@RequestParam int isPost, @RequestBody SearchFilterDto searchFilterDto) {
        return ResponseEntity.ok(farmsService.farmsFetch(searchFilterDto, isPost == 1));
    }

    @PostMapping("filter")
    public ResponseEntity<List<Land>> random(@RequestParam int isPost, @RequestBody SearchFilterDto searchFilterDto) {
        List<FarmsDto> farmsDtoList = farmsService.farmsFetch(searchFilterDto, isPost == 1);
        return ResponseEntity.ok(FarmsService.makeLands(farmsDtoList));
    }
}
